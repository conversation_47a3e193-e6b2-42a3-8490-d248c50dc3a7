import {
  AnalysisConfig,
  DrawResult,
  StatResult,
  ProgressInfo,
  Occurrence,
} from '@/models/types';

// Worker 事件處理
self.onmessage = (e) => {
  const { type, data } = e.data;

  if (type === 'init') {
    const results: DrawResult[] = JSON.parse(data.results);
    const config: AnalysisConfig = JSON.parse(data.config);
    analyzeLotto(results, config);
  }
};

function analyzeLotto(results: DrawResult[], config: AnalysisConfig) {
  const statResults = new Map<string, StatResult>();
  const occurrenceResults = new Map<string, Occurrence>(); // 用來統計每個號碼的出現次數

  const predictIndex = results.length - 1 + config.lookAheadCount;
  const totalProgress = estimateTotalProgress(results, config);
  let progress = 0;

  function processBatch(startIndex: number) {
    const batchSize = 100; // 控制每次處理的筆數，避免長時間佔用 Worker

    // 重新設計邏輯：先收集所有可能的組合，然後檢查連續性
    const allCombinations = new Map<string, {
      firstGroup: number[];
      secondGroup: number[];
      gap: number;
      targetGap: number;
      occurrences: Array<{
        i: number;
        j: number;
        k: number;
        targetGroups: number[][];
      }>;
    }>();

    // 第一步：收集所有組合
    for (
      let i = startIndex;
      i <
      Math.min(results.length - config.lookAheadCount, startIndex + batchSize);
      i++
    ) {
      const firstGroupsGen = getCombinationsGenerator(
        results[i].numbers,
        config.firstGroupSize
      );
      const firstGroups = Array.from(firstGroupsGen);

      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        const secondGroupsGen = getCombinationsGenerator(
          results[j].numbers,
          config.secondGroupSize
        );
        const secondGroups = Array.from(secondGroupsGen);
        const gap = j - i;

        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;

          const targetGap = k - j;

          for (const firstGroup of firstGroups) {
            for (const secondGroup of secondGroups) {
              const key = `${firstGroup.join(',')}-${secondGroup.join(
                ','
              )}-${gap}-${targetGap}`;

              if (!allCombinations.has(key)) {
                allCombinations.set(key, {
                  firstGroup,
                  secondGroup,
                  gap,
                  targetGap,
                  occurrences: [],
                });
              }

              const combo = allCombinations.get(key);
              if (!combo) continue;

              if (k < results.length) {
                const targetGroupsGen = getCombinationsGenerator(
                  results[k].numbers,
                  config.targetGroupSize
                );
                const targetGroups = Array.from(targetGroupsGen);

                combo.occurrences.push({
                  i,
                  j,
                  k,
                  targetGroups,
                });
              } else if (k === predictIndex) {
                combo.occurrences.push({
                  i,
                  j,
                  k,
                  targetGroups: [],
                });
              }
            }
          }
        }
      }
    }

    // 第二步：檢查連續性並記錄結果
    for (const [key, combo] of allCombinations.entries()) {
      if (combo.occurrences.length === 0) continue;

      // 按 k 值排序，檢查連續性
      combo.occurrences.sort((a, b) => a.k - b.k);

      // 重新設計連續性檢查邏輯
      const continuousSequences: Array<typeof combo.occurrences> = [];
      let currentSequence: typeof combo.occurrences = [];
      let expectedK = -1;

      for (const occurrence of combo.occurrences) {
        if (occurrence.k === predictIndex) {
          // 預測位置，只有當前面有連續記錄時才加入
          if (currentSequence.length > 0) {
            currentSequence.push(occurrence);
          }
        } else {
          // 實際記錄位置
          if (occurrence.targetGroups.length > 0) {
            // 有匹配的 targetGroups
            if (expectedK === -1 || occurrence.k === expectedK) {
              // 開始新序列或繼續連續
              currentSequence.push(occurrence);
              expectedK = occurrence.k + 1;
            } else {
              // 不連續，保存當前序列並開始新序列
              if (currentSequence.length > 0) {
                continuousSequences.push([...currentSequence]);
              }
              currentSequence = [occurrence];
              expectedK = occurrence.k + 1;
            }
          } else {
            // 沒有匹配的 targetGroups，中斷連續性
            if (currentSequence.length > 0) {
              continuousSequences.push([...currentSequence]);
            }
            currentSequence = [];
            expectedK = -1;
          }
        }
      }

      // 保存最後一個序列
      if (currentSequence.length > 0) {
        continuousSequences.push(currentSequence);
      }

      // 只保留最新的連續序列（最後一個）
      if (continuousSequences.length > 0) {
        const latestSequence = continuousSequences[continuousSequences.length - 1];

        // 檢查最新序列是否有效（必須有實際的匹配記錄）
        const hasValidMatches = latestSequence.some(occ =>
          occ.k < results.length && occ.targetGroups.length > 0
        );

        if (hasValidMatches) {
          const occurrence: Occurrence = {
            count: latestSequence.filter(occ => occ.k < results.length && occ.targetGroups.length > 0).length,
            periods: [],
            isPredict: false,
          };

          // 記錄每個有效的出現
          for (const occ of latestSequence) {
            if (occ.k < results.length && occ.targetGroups.length > 0) {
              occurrence.periods.push({
                firstPeriod: results[occ.i].period,
                secondPeriod: results[occ.j].period,
                targetPeriod: results[occ.k].period,
              });

              // 記錄每個 targetGroup
              for (const targetGroup of occ.targetGroups) {
                const targetKey = `${combo.firstGroup.join(',')}-${combo.secondGroup.join(
                  ','
                )}-${combo.gap}-${combo.targetGap}-${targetGroup.join(',')}`;

                if (!statResults.has(targetKey)) {
                  const newStat: StatResult = {
                    firstNumbers: combo.firstGroup,
                    secondNumbers: combo.secondGroup,
                    targetNumbers: targetGroup,
                    gap: combo.gap,
                    targetGap: combo.targetGap,
                    targetMatches: 1,
                    targetProbability: 0,
                    rank: 0,
                  };
                  statResults.set(targetKey, newStat);
                } else {
                  const stat = statResults.get(targetKey);
                  if (stat) {
                    stat.targetMatches++;
                  }
                }
              }
            } else if (occ.k === predictIndex) {
              occurrence.periods.push({
                firstPeriod: results[occ.i].period,
                secondPeriod: results[occ.j].period,
              });
              occurrence.isPredict = true;
            }
          }

          // 只有當有實際匹配記錄時才保存
          if (occurrence.count > 0) {
            occurrenceResults.set(key, occurrence);
          }
        }
      }
    }

    // 更新進度
    for (
      let i = startIndex;
      i <
      Math.min(results.length - config.lookAheadCount, startIndex + batchSize);
      i++
    ) {
      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;
          progress++;
          postProgress(progress, totalProgress);
        }
      }
    }

    postProgress(progress, totalProgress);

    if (startIndex + batchSize < results.length - config.lookAheadCount) {
      setTimeout(() => processBatch(startIndex + batchSize), 0); // 讓 Worker 釋放資源
    } else {
      finalizeResults();
    }
  }

  function finalizeResults() {
    const results: StatResult[] = [];
    const matchResults: Map<number, StatResult[]> = new Map();

    for (const stat of statResults.values()) {
      const key = `${stat.firstNumbers.join(',')}-${stat.secondNumbers.join(
        ','
      )}-${stat.gap}-${stat.targetGap}`;

      const occurrence = occurrenceResults.get(key);

      if (!occurrence?.isPredict) continue;

      if (!occurrence || occurrence.count <= 0) continue;

      stat.targetProbability = stat.targetMatches / occurrence.count;

      // 現在統計結果會是100%機率，因為只保留連續出現的組合
      // 移除所有篩選條件，只要有出現就顯示

      if (!matchResults.has(stat.targetMatches)) {
        matchResults.set(stat.targetMatches, []);
      }
      matchResults.get(stat.targetMatches)?.push(stat);
      results.push(stat);
    }

    const sortedResults = sortAndRankResults(results, occurrenceResults);

    postMessage({
      type: 'complete',
      data: sortedResults,
      occurrences: occurrenceResults,
      matchData: matchResults,
    });
  }

  processBatch(0);
}

function* generateCombinationsGenerator(
  nums: number[],
  choose: number,
  start = 0,
  current: number[] = []
): Generator<number[]> {
  if (current.length === choose) {
    yield [...current];
    return;
  }
  for (let i = start; i < nums.length; i++) {
    current.push(nums[i]);
    yield* generateCombinationsGenerator(nums, choose, i + 1, current);
    current.pop();
  }
}

// 緩存 function 可選性使用，但可儘量在短生命週期內使用
function getCombinationsGenerator(
  nums: number[],
  size: number
): Generator<number[]> {
  // 直接回傳 generator，不做全量緩存
  return generateCombinationsGenerator(nums, size);
}

function estimateTotalProgress(results: DrawResult[], config: AnalysisConfig) {
  const predictIndex = results.length - 1 + config.lookAheadCount;
  let total = 0;

  for (let i = 0; i < results.length - config.lookAheadCount; i++) {
    for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
      for (let k = j + 1; k < results.length && k - j <= config.maxRange; k++) {
        if (k > predictIndex) break;

        total++;
      }
    }
  }
  return total;
}

function sortAndRankResults(
  results: StatResult[],
  occurrences: Map<string, Occurrence>
): StatResult[] {
  // 使用一次排序來同時比較三個條件
  results.sort((a, b) => {
    // 依照 targetProbability 進行降冪排序
    const probabilityDiff = b.targetProbability - a.targetProbability;
    if (probabilityDiff !== 0) {
      return probabilityDiff;
    }

    const aOccurrence =
      occurrences.get(
        a.firstNumbers.join(',') + '-' + a.secondNumbers.join(',') + '-' + a.gap
      )?.count || 0;
    const bOccurrence =
      occurrences.get(
        b.firstNumbers.join(',') + '-' + b.secondNumbers.join(',') + '-' + b.gap
      )?.count || 0;

    // 當 targetProbability 相同時，比較 occurrences (降冪排序)
    const occurrencesDiff = bOccurrence - aOccurrence;
    if (occurrencesDiff !== 0) {
      return occurrencesDiff;
    }

    // 如果還是一樣，則依照 firstGroup 陣列中的第一個數字 (升冪排序)
    // 預防 firstGroup 為空的情況，這邊預設若沒有值就設為 Infinity (較後)
    const aFirst =
      a.firstNumbers && a.firstNumbers.length > 0
        ? a.firstNumbers[0]
        : Infinity;
    const bFirst =
      b.firstNumbers && b.firstNumbers.length > 0
        ? b.firstNumbers[0]
        : Infinity;
    return aFirst - bFirst;
  });

  // 加上 rank 屬性 (依照排序後的索引位置)
  return results.map((result, index) => {
    result.rank = index + 1;
    return result;
  });
}

function postProgress(progress: number, total: number) {
  const info: ProgressInfo = {
    stage: 'processing',
    progress,
    total,
  };
  postMessage({ type: 'progress', data: info });
}
