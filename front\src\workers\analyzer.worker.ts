import {
  AnalysisConfig,
  DrawResult,
  StatResult,
  ProgressInfo,
  Occurrence,
} from '@/models/types';

// Worker 事件處理
self.onmessage = (e) => {
  const { type, data } = e.data;

  if (type === 'init') {
    const results: DrawResult[] = JSON.parse(data.results);
    const config: AnalysisConfig = JSON.parse(data.config);
    analyzeLotto(results, config);
  }
};

function analyzeLotto(results: DrawResult[], config: AnalysisConfig) {
  const statResults = new Map<string, StatResult>();
  const occurrenceResults = new Map<string, Occurrence>(); // 用來統計每個號碼的出現次數
  // 追蹤每個組合的連續狀態
  const continuousTracker = new Map<string, { lastIndex: number; isActive: boolean }>();

  const predictIndex = results.length - 1 + config.lookAheadCount;
  const totalProgress = estimateTotalProgress(results, config);
  let progress = 0;

  function processBatch(startIndex: number) {
    const batchSize = 100; // 控制每次處理的筆數，避免長時間佔用 Worker

    // 先計算 Occurrence 統計 - 修改為只保留最新且連續出現的組合
    for (
      let i = startIndex;
      i <
      Math.min(results.length - config.lookAheadCount, startIndex + batchSize);
      i++
    ) {
      // 第一組號碼組合
      const firstGroupsGen = getCombinationsGenerator(
        results[i].numbers,
        config.firstGroupSize
      );
      const firstGroups = Array.from(firstGroupsGen);

      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        // 第二組號碼組合
        const secondGroupsGen = getCombinationsGenerator(
          results[j].numbers,
          config.secondGroupSize
        );
        const secondGroups = Array.from(secondGroupsGen);
        const gap = j - i;

        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;

          const targetGap = k - j;

          // 計算 Occurrence 統計 - 追蹤連續出現的組合
          for (const firstGroup of firstGroups) {
            for (const secondGroup of secondGroups) {
              const key = `${firstGroup.join(',')}-${secondGroup.join(
                ','
              )}-${gap}-${targetGap}`;

              if (k < results.length) {
                // 檢查這個組合是否在目標位置有匹配的號碼
                const targetGroupsGen = getCombinationsGenerator(
                  results[k].numbers,
                  config.targetGroupSize
                );
                const targetGroups = Array.from(targetGroupsGen);

                let hasMatch = false;
                const matchedTargetGroups: number[][] = [];

                for (const targetGroup of targetGroups) {
                  const targetKey = `${firstGroup.join(',')}-${secondGroup.join(
                    ','
                  )}-${gap}-${targetGap}-${targetGroup.join(',')}`;

                  // 檢查連續性
                  const tracker = continuousTracker.get(targetKey);
                  const expectedIndex = k;

                  if (!tracker) {
                    // 新的組合，開始追蹤
                    continuousTracker.set(targetKey, { lastIndex: expectedIndex, isActive: true });
                    matchedTargetGroups.push(targetGroup);
                    hasMatch = true;
                  } else if (tracker.isActive && tracker.lastIndex === expectedIndex - 1) {
                    // 連續出現
                    tracker.lastIndex = expectedIndex;
                    matchedTargetGroups.push(targetGroup);
                    hasMatch = true;
                  } else {
                    // 中斷了，重新開始
                    tracker.lastIndex = expectedIndex;
                    tracker.isActive = true;
                    matchedTargetGroups.push(targetGroup);
                    hasMatch = true;

                    // 清除之前的統計記錄，重新開始計算
                    statResults.delete(targetKey);
                    const occKey = `${firstGroup.join(',')}-${secondGroup.join(',')}-${gap}-${targetGap}`;
                    occurrenceResults.delete(occKey);
                  }
                }

                // 記錄匹配的組合
                if (hasMatch) {
                  for (const targetGroup of matchedTargetGroups) {
                    const targetKey = `${firstGroup.join(',')}-${secondGroup.join(
                      ','
                    )}-${gap}-${targetGap}-${targetGroup.join(',')}`;

                    if (!statResults.has(targetKey)) {
                      const newStat: StatResult = {
                        firstNumbers: firstGroup,
                        secondNumbers: secondGroup,
                        targetNumbers: targetGroup,
                        gap: gap,
                        targetGap: targetGap,
                        targetMatches: 1,
                        targetProbability: 0,
                        rank: 0,
                      };
                      statResults.set(targetKey, newStat);
                    } else {
                      const stat = statResults.get(targetKey);
                      if (stat) {
                        stat.targetMatches++;
                      }
                    }
                  }

                  // 記錄 occurrence
                  const occurrence: Occurrence = occurrenceResults.get(key) || {
                    count: 0,
                    periods: [],
                    isPredict: false,
                  };

                  occurrence.count++;
                  occurrence.periods.push({
                    firstPeriod: results[i].period,
                    secondPeriod: results[j].period,
                    targetPeriod: results[k].period,
                  });
                  occurrenceResults.set(key, occurrence);
                }
              } else if (k === predictIndex) {
                // 對於預測位置，只有當之前有連續記錄時才標記為預測
                const occurrence = occurrenceResults.get(key);
                if (occurrence && occurrence.count > 0) {
                  occurrence.periods.push({
                    firstPeriod: results[i].period,
                    secondPeriod: results[j].period,
                  });
                  occurrence.isPredict = true;
                  occurrenceResults.set(key, occurrence);
                }
              }
            }
          }
        }
      }
    }

    // 更新進度
    for (
      let i = startIndex;
      i <
      Math.min(results.length - config.lookAheadCount, startIndex + batchSize);
      i++
    ) {
      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;
          progress++;
          postProgress(progress, totalProgress);
        }
      }
    }

    postProgress(progress, totalProgress);

    if (startIndex + batchSize < results.length - config.lookAheadCount) {
      setTimeout(() => processBatch(startIndex + batchSize), 0); // 讓 Worker 釋放資源
    } else {
      finalizeResults();
    }
  }

  function finalizeResults() {
    const results: StatResult[] = [];
    const matchResults: Map<number, StatResult[]> = new Map();

    for (const stat of statResults.values()) {
      const key = `${stat.firstNumbers.join(',')}-${stat.secondNumbers.join(
        ','
      )}-${stat.gap}-${stat.targetGap}`;

      const occurrence = occurrenceResults.get(key);

      if (!occurrence?.isPredict) continue;

      if (!occurrence || occurrence.count <= 0) continue;

      stat.targetProbability = stat.targetMatches / occurrence.count;

      // 現在統計結果會是100%機率，因為只保留連續出現的組合
      // 移除所有篩選條件，只要有出現就顯示

      if (!matchResults.has(stat.targetMatches)) {
        matchResults.set(stat.targetMatches, []);
      }
      matchResults.get(stat.targetMatches)?.push(stat);
      results.push(stat);
    }

    const sortedResults = sortAndRankResults(results, occurrenceResults);

    postMessage({
      type: 'complete',
      data: sortedResults,
      occurrences: occurrenceResults,
      matchData: matchResults,
    });
  }

  processBatch(0);
}

function* generateCombinationsGenerator(
  nums: number[],
  choose: number,
  start = 0,
  current: number[] = []
): Generator<number[]> {
  if (current.length === choose) {
    yield [...current];
    return;
  }
  for (let i = start; i < nums.length; i++) {
    current.push(nums[i]);
    yield* generateCombinationsGenerator(nums, choose, i + 1, current);
    current.pop();
  }
}

// 緩存 function 可選性使用，但可儘量在短生命週期內使用
function getCombinationsGenerator(
  nums: number[],
  size: number
): Generator<number[]> {
  // 直接回傳 generator，不做全量緩存
  return generateCombinationsGenerator(nums, size);
}

function estimateTotalProgress(results: DrawResult[], config: AnalysisConfig) {
  const predictIndex = results.length - 1 + config.lookAheadCount;
  let total = 0;

  for (let i = 0; i < results.length - config.lookAheadCount; i++) {
    for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
      for (let k = j + 1; k < results.length && k - j <= config.maxRange; k++) {
        if (k > predictIndex) break;

        total++;
      }
    }
  }
  return total;
}

function sortAndRankResults(
  results: StatResult[],
  occurrences: Map<string, Occurrence>
): StatResult[] {
  // 使用一次排序來同時比較三個條件
  results.sort((a, b) => {
    // 依照 targetProbability 進行降冪排序
    const probabilityDiff = b.targetProbability - a.targetProbability;
    if (probabilityDiff !== 0) {
      return probabilityDiff;
    }

    const aOccurrence =
      occurrences.get(
        a.firstNumbers.join(',') + '-' + a.secondNumbers.join(',') + '-' + a.gap
      )?.count || 0;
    const bOccurrence =
      occurrences.get(
        b.firstNumbers.join(',') + '-' + b.secondNumbers.join(',') + '-' + b.gap
      )?.count || 0;

    // 當 targetProbability 相同時，比較 occurrences (降冪排序)
    const occurrencesDiff = bOccurrence - aOccurrence;
    if (occurrencesDiff !== 0) {
      return occurrencesDiff;
    }

    // 如果還是一樣，則依照 firstGroup 陣列中的第一個數字 (升冪排序)
    // 預防 firstGroup 為空的情況，這邊預設若沒有值就設為 Infinity (較後)
    const aFirst =
      a.firstNumbers && a.firstNumbers.length > 0
        ? a.firstNumbers[0]
        : Infinity;
    const bFirst =
      b.firstNumbers && b.firstNumbers.length > 0
        ? b.firstNumbers[0]
        : Infinity;
    return aFirst - bFirst;
  });

  // 加上 rank 屬性 (依照排序後的索引位置)
  return results.map((result, index) => {
    result.rank = index + 1;
    return result;
  });
}

function postProgress(progress: number, total: number) {
  const info: ProgressInfo = {
    stage: 'processing',
    progress,
    total,
  };
  postMessage({ type: 'progress', data: info });
}
