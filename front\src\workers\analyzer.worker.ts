import {
  AnalysisConfig,
  DrawResult,
  StatResult,
  ProgressInfo,
  Occurrence,
} from '@/models/types';

// Worker 事件處理
self.onmessage = (e) => {
  const { type, data } = e.data;

  if (type === 'init') {
    const results: DrawResult[] = JSON.parse(data.results);
    const config: AnalysisConfig = JSON.parse(data.config);
    analyzeLotto(results, config);
  }
};

function analyzeLotto(results: DrawResult[], config: AnalysisConfig) {
  const statResults = new Map<string, StatResult>();
  const occurrenceResults = new Map<string, Occurrence>(); // 用來統計每個號碼的出現次數

  const predictIndex = results.length - 1 + config.lookAheadCount;
  const totalProgress = estimateTotalProgress(results, config);
  let progress = 0;

  function processBatch(startIndex: number) {
    const batchSize = 50; // 減少批次大小，更頻繁地釋放控制權

    // 優化：為每個具體的 targetGroup 組合單獨追蹤連續性
    // 追蹤每個完整組合的連續狀態 (包含 targetGroup)
    const continuousTracker = new Map<string, {
      lastK: number;
      consecutiveCount: number;
      periods: Array<{
        firstPeriod: string;
        secondPeriod: string;
        targetPeriod: string;
      }>;
    }>();

    // 預先計算當前批次所需的所有組合
    const allCombinations = new Map<number, number[][]>();
    const batchEndIndex = Math.min(results.length - config.lookAheadCount, startIndex + batchSize);

    // 預先計算所有可能用到的組合
    for (let i = startIndex; i < batchEndIndex; i++) {
      if (!allCombinations.has(i)) {
        const firstGroupsGen = getCombinationsGenerator(
          results[i].numbers,
          config.firstGroupSize
        );
        allCombinations.set(i, Array.from(firstGroupsGen));
      }

      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        if (!allCombinations.has(j)) {
          const secondGroupsGen = getCombinationsGenerator(
            results[j].numbers,
            config.secondGroupSize
          );
          allCombinations.set(j, Array.from(secondGroupsGen));
        }

        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;

          if (k < results.length && !allCombinations.has(k)) {
            const targetGroupsGen = getCombinationsGenerator(
              results[k].numbers,
              config.targetGroupSize
            );
            allCombinations.set(k, Array.from(targetGroupsGen));
          }
        }
      }
    }

    // 主處理迴圈
    for (let i = startIndex; i < batchEndIndex; i++) {
      const firstGroups = allCombinations.get(i);
      if (!firstGroups) continue;

      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        const secondGroups = allCombinations.get(j);
        if (!secondGroups) continue;

        const gap = j - i;

        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;

          const targetGap = k - j;
          let targetGroups: number[][] = [];

          if (k < results.length) {
            const cachedTargetGroups = allCombinations.get(k);
            if (cachedTargetGroups) {
              targetGroups = cachedTargetGroups;
            }
          }

          // 預先計算基礎 key 以避免重複字符串操作
          const baseKeyParts = [gap.toString(), targetGap.toString()];

          for (const firstGroup of firstGroups) {
            const firstGroupStr = firstGroup.join(',');

            for (const secondGroup of secondGroups) {
              const secondGroupStr = secondGroup.join(',');
              const baseKey = `${firstGroupStr}-${secondGroupStr}-${baseKeyParts[0]}-${baseKeyParts[1]}`;

              if (k < results.length && targetGroups.length > 0) {
                // 追蹤當前位置存在的 targetGroup
                const currentTargetKeys = new Set<string>();

                // 為每個具體的 targetGroup 單獨追蹤
                for (const targetGroup of targetGroups) {
                  const targetGroupStr = targetGroup.join(',');
                  const fullKey = `${baseKey}-${targetGroupStr}`;
                  currentTargetKeys.add(fullKey);

                  const tracker = continuousTracker.get(fullKey);

                  if (!tracker) {
                    // 新的組合，開始追蹤
                    continuousTracker.set(fullKey, {
                      lastK: k,
                      consecutiveCount: 1,
                      periods: [{
                        firstPeriod: results[i].period,
                        secondPeriod: results[j].period,
                        targetPeriod: results[k].period,
                      }],
                    });
                  } else if (tracker.lastK + 1 === k) {
                    // 連續出現
                    tracker.lastK = k;
                    tracker.consecutiveCount++;
                    tracker.periods.push({
                      firstPeriod: results[i].period,
                      secondPeriod: results[j].period,
                      targetPeriod: results[k].period,
                    });
                  } else {
                    // 不連續，重新開始
                    tracker.lastK = k;
                    tracker.consecutiveCount = 1;
                    tracker.periods = [{
                      firstPeriod: results[i].period,
                      secondPeriod: results[j].period,
                      targetPeriod: results[k].period,
                    }];
                  }
                }

                // 優化：只檢查相關的 tracker keys，避免遍歷整個 Map
                const keysToDelete: string[] = [];
                for (const [trackerKey] of continuousTracker.entries()) {
                  if (trackerKey.startsWith(baseKey + '-') && !currentTargetKeys.has(trackerKey)) {
                    keysToDelete.push(trackerKey);
                  }
                }

                // 批量刪除
                for (const key of keysToDelete) {
                  continuousTracker.delete(key);
                }
              } else if (k === predictIndex) {
                // 預測位置，檢查所有相關的連續記錄
                for (const [trackerKey, tracker] of continuousTracker.entries()) {
                  if (trackerKey.startsWith(baseKey + '-') && tracker.consecutiveCount > 0) {
                    // 記錄 occurrence (使用完整的 key)
                    const occurrence: Occurrence = {
                      count: tracker.consecutiveCount,
                      periods: [...tracker.periods],
                      isPredict: true,
                    };

                    // 添加預測期間
                    occurrence.periods.push({
                      firstPeriod: results[i].period,
                      secondPeriod: results[j].period,
                    });

                    occurrenceResults.set(trackerKey, occurrence);

                    // 記錄統計結果
                    if (!statResults.has(trackerKey)) {
                      const targetGroupPart = trackerKey.substring(baseKey.length + 1);
                      const targetGroup = targetGroupPart.split(',').map(Number);

                      const newStat: StatResult = {
                        firstNumbers: firstGroup,
                        secondNumbers: secondGroup,
                        targetNumbers: targetGroup,
                        gap: gap,
                        targetGap: targetGap,
                        targetMatches: tracker.consecutiveCount,
                        targetProbability: 0,
                        rank: 0,
                      };
                      statResults.set(trackerKey, newStat);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    // 優化進度更新 - 減少頻繁的進度報告
    const batchProgressIncrement = estimateBatchProgress(startIndex, Math.min(results.length - config.lookAheadCount, startIndex + batchSize), config);
    progress += batchProgressIncrement;

    // 只在進度有顯著變化時才報告
    if (progress % Math.max(1, Math.floor(totalProgress / 100)) === 0) {
      postProgress(progress, totalProgress);
    }

    if (startIndex + batchSize < results.length - config.lookAheadCount) {
      // 使用 requestIdleCallback 的替代方案，讓瀏覽器有機會處理其他任務
      setTimeout(() => processBatch(startIndex + batchSize), 1);
    } else {
      postProgress(totalProgress, totalProgress); // 確保最終進度為 100%
      finalizeResults();
    }
  }

  // 估算批次進度增量
  function estimateBatchProgress(startIndex: number, endIndex: number, config: AnalysisConfig): number {
    let batchProgress = 0;
    for (let i = startIndex; i < endIndex; i++) {
      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;
          batchProgress++;
        }
      }
    }
    return batchProgress;
  }

  function finalizeResults() {
    const results: StatResult[] = [];
    const matchResults: Map<number, StatResult[]> = new Map();

    for (const stat of statResults.values()) {
      const key = `${stat.firstNumbers.join(',')}-${stat.secondNumbers.join(
        ','
      )}-${stat.gap}-${stat.targetGap}`;

      const occurrence = occurrenceResults.get(key);

      if (!occurrence?.isPredict) continue;

      if (!occurrence || occurrence.count <= 0) continue;

      stat.targetProbability = stat.targetMatches / occurrence.count;

      // 現在統計結果會是100%機率，因為只保留連續出現的組合
      // 移除所有篩選條件，只要有出現就顯示

      if (!matchResults.has(stat.targetMatches)) {
        matchResults.set(stat.targetMatches, []);
      }
      matchResults.get(stat.targetMatches)?.push(stat);
      results.push(stat);
    }

    const sortedResults = sortAndRankResults(results, occurrenceResults);

    postMessage({
      type: 'complete',
      data: sortedResults,
      occurrences: occurrenceResults,
      matchData: matchResults,
    });
  }

  processBatch(0);
}

// 優化的組合生成器 - 使用迭代而非遞歸
function generateCombinations(nums: number[], choose: number): number[][] {
  if (choose === 0) return [[]];
  if (choose > nums.length) return [];

  const result: number[][] = [];
  const indices = Array.from({ length: choose }, (_, i) => i);

  while (true) {
    // 生成當前組合
    result.push(indices.map(i => nums[i]));

    // 找到需要增加的最右邊的索引
    let i = choose - 1;
    while (i >= 0 && indices[i] === nums.length - choose + i) {
      i--;
    }

    if (i < 0) break; // 所有組合都已生成

    // 增加索引並重置後續索引
    indices[i]++;
    for (let j = i + 1; j < choose; j++) {
      indices[j] = indices[j - 1] + 1;
    }
  }

  return result;
}

// 帶緩存的組合生成器
const combinationCache = new Map<string, number[][]>();

function getCombinationsGenerator(nums: number[], size: number): Generator<number[]> {
  const cacheKey = `${nums.join(',')}-${size}`;

  let combinations = combinationCache.get(cacheKey);
  if (!combinations) {
    combinations = generateCombinations(nums, size);
    // 限制緩存大小，避免記憶體過度使用
    if (combinationCache.size < 1000) {
      combinationCache.set(cacheKey, combinations);
    }
  }

  return (function* () {
    for (const combination of combinations) {
      yield combination;
    }
  })();
}

function estimateTotalProgress(results: DrawResult[], config: AnalysisConfig) {
  const predictIndex = results.length - 1 + config.lookAheadCount;
  let total = 0;

  for (let i = 0; i < results.length - config.lookAheadCount; i++) {
    for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
      for (let k = j + 1; k < results.length && k - j <= config.maxRange; k++) {
        if (k > predictIndex) break;

        total++;
      }
    }
  }
  return total;
}

function sortAndRankResults(
  results: StatResult[],
  occurrences: Map<string, Occurrence>
): StatResult[] {
  // 使用一次排序來同時比較三個條件
  results.sort((a, b) => {
    // 依照 targetProbability 進行降冪排序
    const probabilityDiff = b.targetProbability - a.targetProbability;
    if (probabilityDiff !== 0) {
      return probabilityDiff;
    }

    const aOccurrence =
      occurrences.get(
        a.firstNumbers.join(',') + '-' + a.secondNumbers.join(',') + '-' + a.gap
      )?.count || 0;
    const bOccurrence =
      occurrences.get(
        b.firstNumbers.join(',') + '-' + b.secondNumbers.join(',') + '-' + b.gap
      )?.count || 0;

    // 當 targetProbability 相同時，比較 occurrences (降冪排序)
    const occurrencesDiff = bOccurrence - aOccurrence;
    if (occurrencesDiff !== 0) {
      return occurrencesDiff;
    }

    // 如果還是一樣，則依照 firstGroup 陣列中的第一個數字 (升冪排序)
    // 預防 firstGroup 為空的情況，這邊預設若沒有值就設為 Infinity (較後)
    const aFirst =
      a.firstNumbers && a.firstNumbers.length > 0
        ? a.firstNumbers[0]
        : Infinity;
    const bFirst =
      b.firstNumbers && b.firstNumbers.length > 0
        ? b.firstNumbers[0]
        : Infinity;
    return aFirst - bFirst;
  });

  // 加上 rank 屬性 (依照排序後的索引位置)
  return results.map((result, index) => {
    result.rank = index + 1;
    return result;
  });
}

function postProgress(progress: number, total: number) {
  const info: ProgressInfo = {
    stage: 'processing',
    progress,
    total,
  };
  postMessage({ type: 'progress', data: info });
}
